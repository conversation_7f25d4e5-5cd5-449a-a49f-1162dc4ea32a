import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Chip
} from '@mui/material';
import {
  VideoLibrary,
  Photo,
  Remove
} from '@mui/icons-material';

interface UsageTransaction {
  id: string;
  type: string;
  amount: number;
  balanceAfter: number;
  description: string;
  compressionJobId?: string;
  paymentId?: string;
  createdAt: string;
}

interface UsageHistoryProps {
  token: string;
}

const UsageHistory: React.FC<UsageHistoryProps> = ({ token }) => {
  const [transactions, setTransactions] = useState<UsageTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchTransactions();
  }, [token]);

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/credits/transactions?limit=100', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch transaction history');
      }

      const data = await response.json();
      // Filter for credit usage only (negative amounts)
      const usageTransactions = data.filter((t: UsageTransaction) => 
        t.type === 'Deduction' || t.amount < 0
      );
      setTransactions(usageTransactions);
    } catch (err) {
      console.error('Error fetching usage history:', err);
      setError('Failed to load usage history');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCreditsSpent = (amount: number) => {
    return `${Math.abs(amount)} Credits`;
  };

  const getActionIcon = (description: string) => {
    if (description.toLowerCase().includes('video')) {
      return <VideoLibrary sx={{ fontSize: 16, color: 'primary.main' }} />;
    } else if (description.toLowerCase().includes('photo')) {
      return <Photo sx={{ fontSize: 16, color: 'secondary.main' }} />;
    }
    return <Remove sx={{ fontSize: 16, color: 'text.secondary' }} />;
  };

  const getActionType = (description: string) => {
    if (description.toLowerCase().includes('video')) {
      return 'Video';
    } else if (description.toLowerCase().includes('photo')) {
      return 'Photo';
    }
    return 'Other';
  };

  const getQualityFromDescription = (description: string) => {
    const qualityMatch = description.match(/\((.*?)\s+quality\)/i);
    return qualityMatch ? qualityMatch[1] : 'Standard';
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ px: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  if (transactions.length === 0) {
    return (
      <Box sx={{ px: 3, textAlign: 'center', py: 4 }}>
        <Typography variant="body1" sx={{ color: 'text.secondary', mb: 2 }}>
          No usage history found
        </Typography>
        <Typography variant="body2" sx={{ color: 'text.secondary' }}>
          Your compression activities will appear here
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ px: 3 }}>
      <Typography variant="h6" sx={{ mb: 3, fontWeight: 500 }}>
        Usage History
      </Typography>
      
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 600 }}>Date</TableCell>
              <TableCell sx={{ fontWeight: 600 }}>Action</TableCell>
              <TableCell sx={{ fontWeight: 600 }}>Credits Spent</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {transactions.map((transaction) => (
              <TableRow key={transaction.id} hover>
                <TableCell>
                  {formatDate(transaction.createdAt)}
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                    {getActionIcon(transaction.description)}
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {transaction.description}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                        <Chip
                          label={getActionType(transaction.description)}
                          size="small"
                          variant="outlined"
                          color={getActionType(transaction.description) === 'Video' ? 'primary' : 'secondary'}
                        />
                        <Chip
                          label={getQualityFromDescription(transaction.description)}
                          size="small"
                          variant="outlined"
                        />
                      </Box>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Remove sx={{ color: 'error.main', fontSize: 16 }} />
                    <Typography
                      sx={{
                        color: 'error.main',
                        fontWeight: 500
                      }}
                    >
                      {formatCreditsSpent(transaction.amount)}
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default UsageHistory;
