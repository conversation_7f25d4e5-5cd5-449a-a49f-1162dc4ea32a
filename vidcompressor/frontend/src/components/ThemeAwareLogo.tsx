import React from 'react';
import { Box, BoxProps } from '@mui/material';
import { useTheme } from '../contexts/ThemeContext';

interface ThemeAwareLogoProps extends Omit<BoxProps, 'component' | 'src' | 'alt'> {
  alt?: string;
  size?: number;
}

const ThemeAwareLogo: React.FC<ThemeAwareLogoProps> = ({
  alt = "Gallery Tuner",
  size = 32,
  sx,
  ...props
}) => {
  const { effectiveTheme } = useTheme();
  
  const logoSrc = effectiveTheme === 'dark' 
    ? '/gallery_tuner_dark_mode.png' 
    : '/gallery_tuner_light_mode.png';

  return (
    <Box
      component="img"
      src={logoSrc}
      alt={alt}
      sx={{
        width: size,
        height: size,
        objectFit: 'contain',
        ...sx
      }}
      {...props}
    />
  );
};

export default ThemeAwareLogo;
