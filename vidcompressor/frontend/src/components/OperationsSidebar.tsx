import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Divider,
  Chip,
  IconButton,
  Tooltip,
  Collapse,
  List,
  ListItem,
  Badge
} from '@mui/material';
import {
  Compress,
  SelectAll,
  Clear,
  CleaningServices,
  ExpandMore,
  ExpandLess,
  ExitToApp
} from '@mui/icons-material';

interface OperationsSidebarProps {
  // Selection state
  selectedItems: Set<string>;
  totalItems: number;
  expiredItemsCount: number;

  // Handlers for operations
  onSelectAll: () => void;
  onClearSelection: () => void;
  onCompressSelected: () => void;
  onRemoveSelected: () => void;
  onClearExpired: () => void;

  // UI state
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

const OperationsSidebar: React.FC<OperationsSidebarProps> = ({
  selectedItems,
  totalItems,
  expiredItemsCount,
  onSelectAll,
  onClearSelection,
  onCompressSelected,
  onRemoveSelected,
  onClearExpired,
  isCollapsed = false,
  onToggleCollapse
}) => {

  const selectedCount = selectedItems.size;
  const hasSelection = selectedCount > 0;
  const hasExpiredItems = expiredItemsCount > 0;

  return (
    <Paper
      elevation={0}
      sx={{
        width: isCollapsed ? { xs: '100%', lg: 60 } : { xs: '100%', lg: 280 },
        height: 'fit-content',
        maxHeight: { xs: 'none', lg: 'calc(100vh - 40px)' },
        position: { xs: 'static', lg: 'sticky' },
        top: { lg: 20 },
        alignSelf: 'flex-start',
        backgroundColor: 'grey.50',
        border: 1,
        borderColor: 'divider',
        borderRadius: 2,
        transition: 'width 0.3s ease, top 0.2s ease',
        overflow: 'hidden',
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        flexShrink: 0,
        zIndex: 100
      }}
    >
      {/* Header */}
      <Box
        sx={{
          p: isCollapsed ? 1 : 2,
          borderBottom: 1,
          borderColor: 'divider',
          display: 'flex',
          alignItems: 'center',
          justifyContent: isCollapsed ? { xs: 'space-between', lg: 'center' } : 'space-between',
          backgroundColor: 'grey.50',
          minHeight: isCollapsed ? 48 : 'auto'
        }}
      >
        {!isCollapsed && (
          <Typography
            variant="h6"
            sx={{
              fontWeight: 500,
              fontSize: '1.1rem',
              color: 'text.primary'
            }}
          >
            Operations
          </Typography>
        )}
        {isCollapsed && (
          <Typography
            variant="subtitle2"
            sx={{
              display: { xs: 'block', lg: 'none' },
              fontWeight: 500,
              fontSize: '0.875rem',
              color: 'text.secondary'
            }}
          >
            Quick Actions
          </Typography>
        )}
        {onToggleCollapse && (
          <IconButton
            onClick={onToggleCollapse}
            size="small"
            sx={{
              color: 'text.secondary',
              '&:hover': {
                backgroundColor: 'action.hover'
              }
            }}
          >
            {isCollapsed ? <ExpandMore /> : <ExpandLess />}
          </IconButton>
        )}
      </Box>

      {/* Collapsed State - Horizontal Action Bar (Mobile Only) */}
      {isCollapsed && (
        <Box sx={{
          display: { xs: 'flex', lg: 'none' },
          p: 1,
          gap: 1,
          justifyContent: 'center',
          alignItems: 'center',
          flexWrap: 'wrap'
        }}>
          <IconButton
            onClick={onSelectAll}
            size="small"
            disabled={totalItems === 0}
            sx={{
              color: hasSelection ? 'primary.main' : 'text.secondary',
              '&:hover': { backgroundColor: hasSelection ? 'primary.50' : 'action.hover' }
            }}
            title="Select All"
          >
            <Badge badgeContent={selectedCount} color="primary" max={99}>
              <SelectAll />
            </Badge>
          </IconButton>

          <IconButton
            onClick={onClearSelection}
            size="small"
            disabled={selectedItems.size === 0}
            sx={{
              color: 'text.secondary',
              '&:hover': { backgroundColor: 'action.hover' }
            }}
            title="Clear Selection"
          >
            <Clear />
          </IconButton>

          <IconButton
            onClick={onCompressSelected}
            size="small"
            disabled={selectedItems.size === 0}
            sx={{
              color: 'primary.main',
              '&:hover': { backgroundColor: 'primary.50' }
            }}
            title="Compress Selected"
          >
            <Compress />
          </IconButton>

          <IconButton
            onClick={onRemoveSelected}
            size="small"
            disabled={selectedItems.size === 0}
            sx={{
              color: 'error.main',
              '&:hover': { backgroundColor: 'error.50' }
            }}
            title="Remove Selected"
          >
            <ExitToApp />
          </IconButton>

          {expiredItemsCount > 0 && (
            <IconButton
              onClick={onClearExpired}
              size="small"
              sx={{
                color: 'warning.main',
                '&:hover': { backgroundColor: 'warning.50' }
              }}
              title={`Clear ${expiredItemsCount} Expired Items`}
            >
              <CleaningServices />
            </IconButton>
          )}
        </Box>
      )}

      <Collapse in={!isCollapsed} timeout={300}>
        <Box sx={{ p: 2 }}>
          {/* Selection Status */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle2"
              sx={{
                mb: 2,
                fontWeight: 500,
                color: 'text.secondary',
                fontSize: '0.875rem'
              }} 
            >
              Selection
              <Chip
              label={hasSelection ? `${selectedCount} selected` : 'None selected'}
              size="small"
              variant={hasSelection ? 'filled' : 'outlined'}
              color={hasSelection ? 'primary' : 'default'}
              sx={{
                mb: 0,
                fontSize: '0.75rem',
                height: 24,
                ml: 1
              }}
            />
            </Typography>
            
            
            {/* Selection Controls */}
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<SelectAll />}
                onClick={onSelectAll}
                disabled={totalItems === 0}
                sx={{
                  flex: 1,
                  textTransform: 'none',
                  fontSize: '0.75rem',
                  borderRadius: 1,
                  py: 0.5,
                  borderColor: 'divider',
                  color: 'text.secondary',
                  '&:hover': {
                    backgroundColor: 'action.hover',
                    borderColor: 'primary.main'
                  },
                  '&:disabled': {
                    borderColor: 'action.disabled',
                    color: 'action.disabled'
                  }
                }}
              >
                All
              </Button>
              <Button
                variant="outlined"
                size="small"
                startIcon={<Clear />}
                onClick={onClearSelection}
                disabled={!hasSelection}
                sx={{
                  flex: 1,
                  textTransform: 'none',
                  fontSize: '0.75rem',
                  borderRadius: 1,
                  py: 0.5,
                  borderColor: 'divider',
                  color: 'text.secondary',
                  '&:hover': {
                    backgroundColor: 'action.hover',
                    borderColor: 'primary.main'
                  },
                  '&:disabled': {
                    borderColor: 'action.disabled',
                    color: 'action.disabled'
                  }
                }}
              >
                Clear
              </Button>
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* Main Operations */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="subtitle2"
              sx={{
                mb: 2,
                fontWeight: 500,
                color: 'text.secondary',
                fontSize: '0.875rem'
              }}
            >
              Actions
            </Typography>
            
            <List dense sx={{ p: 0 }}>
              {/* Compress Operation */}
              <ListItem sx={{ p: 0, mb: 1 }}>
                <Button
                  variant="contained"
                  fullWidth
                  startIcon={<Compress />}
                  onClick={onCompressSelected}
                  disabled={!hasSelection}
                  sx={{
                    justifyContent: 'flex-start',
                    textTransform: 'none',
                    py: 1.5,
                    backgroundColor: hasSelection ? 'primary.main' : 'action.disabledBackground',
                    borderRadius: 1,
                    boxShadow: hasSelection ? '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)' : 'none',
                    '&:hover': {
                      backgroundColor: hasSelection ? 'primary.dark' : 'action.disabledBackground',
                      boxShadow: hasSelection ? '0 1px 3px 0 rgba(60,64,67,.3), 0 4px 8px 3px rgba(60,64,67,.15)' : 'none'
                    }
                  }}
                >
                  Compress
                </Button>
              </ListItem>

              {/* Remove Access Operation */}
              <ListItem sx={{ p: 0, mb: 1 }}>
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<ExitToApp />}
                  onClick={onRemoveSelected}
                  disabled={!hasSelection}
                  color="error"
                  sx={{
                    justifyContent: 'flex-start',
                    textTransform: 'none',
                    py: 1.5,
                    borderRadius: 1,
                    borderColor: hasSelection ? 'error.main' : 'action.disabled',
                    color: hasSelection ? 'error.main' : 'action.disabled',
                    '&:hover': {
                      backgroundColor: hasSelection ? 'rgba(234, 67, 53, 0.08)' : 'transparent',
                      borderColor: hasSelection ? 'error.dark' : 'action.disabled'
                    }
                  }}
                >
                  Remove Access
                </Button>
              </ListItem>
            </List>
          </Box>

          {/* Maintenance Operations */}
          {hasExpiredItems && (
            <>
              <Divider sx={{ my: 2 }} />
              <Box>
                <Typography
                  variant="subtitle2"
                  sx={{
                    mb: 2,
                    fontWeight: 500,
                    color: 'text.secondary',
                    fontSize: '0.875rem'
                  }}
                >
                  Maintenance
                </Typography>
                
                <List dense sx={{ p: 0 }}>
                  <ListItem sx={{ p: 0 }}>
                    <Button
                      variant="outlined"
                      fullWidth
                      startIcon={<CleaningServices />}
                      onClick={onClearExpired}
                      color="warning"
                      sx={{
                        justifyContent: 'flex-start',
                        textTransform: 'none',
                        py: 1.5,
                        borderRadius: 1,
                        '&:hover': {
                          backgroundColor: 'rgba(255, 193, 7, 0.08)',
                          borderColor: 'warning.dark'
                        }
                      }}
                    >
                      Clear Expired ({expiredItemsCount})
                    </Button>
                  </ListItem>
                </List>
              </Box>
            </>
          )}
        </Box>
      </Collapse>

      {/* Collapsed State - Desktop Only */}
      {isCollapsed && (
        <Box sx={{
          p: 1,
          display: { xs: 'none', lg: 'flex' },
          flexDirection: 'column',
          gap: 1,
          alignItems: 'center',
          justifyContent: 'flex-start'
        }}>
          <Tooltip title="Select All" placement="right">
            <IconButton
              size="small"
              onClick={onSelectAll}
              disabled={totalItems === 0}
              color={hasSelection ? 'primary' : 'default'}
            >
              <Badge badgeContent={selectedCount} color="primary" max={99}>
                <SelectAll />
              </Badge>
            </IconButton>
          </Tooltip>

          <Tooltip title="Clear Selection" placement="right">
            <IconButton
              size="small"
              onClick={onClearSelection}
              disabled={!hasSelection}
              color="default"
            >
              <Clear />
            </IconButton>
          </Tooltip>

          <Tooltip title="Compress" placement="right">
            <IconButton
              size="small"
              onClick={onCompressSelected}
              disabled={!hasSelection}
              color="primary"
            >
              <Compress />
            </IconButton>
          </Tooltip>

          <Tooltip title="Remove Access" placement="right">
            <IconButton
              size="small"
              onClick={onRemoveSelected}
              disabled={!hasSelection}
              color="error"
            >
              <ExitToApp />
            </IconButton>
          </Tooltip>

          {hasExpiredItems && (
            <Tooltip title={`Clear ${expiredItemsCount} expired`} placement="right">
              <IconButton
                size="small"
                onClick={onClearExpired}
                color="warning"
              >
                <Badge badgeContent={expiredItemsCount} color="warning" max={99}>
                  <CleaningServices />
                </Badge>
              </IconButton>
            </Tooltip>
          )}
        </Box>
      )}
    </Paper>
  );
};

export default OperationsSidebar;
