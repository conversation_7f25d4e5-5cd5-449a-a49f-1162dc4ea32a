import React from 'react';
import {
  Box,
  Button,
  ButtonGroup
} from '@mui/material';
import {
  PhotoSizeSelectLarge,
  Schedule
} from '@mui/icons-material';

interface GallerySortControlsProps {
  sortBy: 'none' | 'size' | 'date';
  onSortChange: (sortBy: 'none' | 'size' | 'date') => void;
  totalItems: number;
}

const GallerySortControls: React.FC<GallerySortControlsProps> = ({
  sortBy,
  onSortChange,
  totalItems
}) => {
  if (totalItems === 0) {
    return null; // Don't show sorting controls when there are no items
  }

  return (
    <Box>
      <ButtonGroup
        variant="outlined"
        size="large"
        sx={{
          '& .MuiButton-root': {
            textTransform: 'none',
            fontWeight: 500,
            px: 3,
            py: 1.5,
            borderColor: 'divider',
            borderRadius: 1,
            '&:first-of-type': {
              borderTopRightRadius: 0,
              borderBottomRightRadius: 0,
            },
            '&:last-of-type': {
              borderTopLeftRadius: 0,
              borderBottomLeftRadius: 0,
            }
          }
        }}
      >
        <Button
          startIcon={<PhotoSizeSelectLarge />}
          onClick={() => onSortChange(sortBy === 'size' ? 'none' : 'size')}
          variant={sortBy === 'size' ? 'contained' : 'outlined'}
          sx={{
            backgroundColor: sortBy === 'size' ? 'primary.main' : 'transparent',
            color: sortBy === 'size' ? 'primary.contrastText' : 'text.primary',
            '&:hover': {
              backgroundColor: sortBy === 'size' ? 'primary.dark' : 'action.hover',
              borderColor: 'primary.main'
            }
          }}
        >
          File Size
        </Button>

        <Button
          startIcon={<Schedule />}
          onClick={() => onSortChange(sortBy === 'date' ? 'none' : 'date')}
          variant={sortBy === 'date' ? 'contained' : 'outlined'}
          sx={{
            backgroundColor: sortBy === 'date' ? 'primary.main' : 'transparent',
            color: sortBy === 'date' ? 'primary.contrastText' : 'text.primary',
            '&:hover': {
              backgroundColor: sortBy === 'date' ? 'primary.dark' : 'action.hover',
              borderColor: 'primary.main'
            }
          }}
        >
          Creation Date
        </Button>
      </ButtonGroup>
    </Box>
  );
};

export default GallerySortControls;
