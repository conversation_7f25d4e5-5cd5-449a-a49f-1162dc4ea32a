﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace backend.Migrations
{
    /// <inheritdoc />
    public partial class SeedInitialCreditCosts : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Insert initial credit costs based on the user's requirements
            migrationBuilder.InsertData(
                table: "CreditCosts",
                columns: new[] { "Id", "OperationType", "Cost", "Unit", "Description", "IsActive", "CreatedAt", "UpdatedAt" },
                values: new object[,]
                {
                    { Guid.NewGuid().ToString(), "PhotoCompression", 2, "per_item", "Compress Image - A low-cost, high-margin utility", true, DateTime.UtcNow, DateTime.UtcNow },
                    { Guid.NewGuid().ToString(), "VideoCompressionSD", 6, "per_minute", "SD Video - Base-level video processing", true, DateTime.UtcNow, DateTime.UtcNow },
                    { Guid.NewGuid().ToString(), "VideoCompressionHD", 12, "per_minute", "HD Video - The standard anchor for video pricing", true, DateTime.UtcNow, DateTime.UtcNow },
                    { Guid.NewGuid().ToString(), "VideoCompression4K", 25, "per_minute", "4K/UHD Video - Premium video processing for the highest quality", true, DateTime.UtcNow, DateTime.UtcNow }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Remove the seeded credit costs
            migrationBuilder.DeleteData(
                table: "CreditCosts",
                keyColumn: "OperationType",
                keyValues: new object[] { "PhotoCompression", "VideoCompressionSD", "VideoCompressionHD", "VideoCompression4K" });
        }
    }
}
