﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace backend.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("VidCompressor.Models.CompressionJob", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<string>("BaseUrl")
                        .HasColumnType("text");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CompressedFilePath")
                        .HasColumnType("text");

                    b.Property<string>("CompressedGooglePhotosUrl")
                        .HasColumnType("text");

                    b.Property<long?>("CompressedSizeBytes")
                        .HasColumnType("bigint");

                    b.Property<double?>("CompressionRatio")
                        .HasColumnType("double precision");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("CreditsUsed")
                        .HasColumnType("integer");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<string>("GooglePhotosUrl")
                        .HasColumnType("text");

                    b.Property<string>("InputStoragePath")
                        .HasColumnType("text");

                    b.Property<string>("MediaItemId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("MediaType")
                        .HasColumnType("integer");

                    b.Property<string>("OriginalFilename")
                        .HasColumnType("text");

                    b.Property<int?>("OriginalHeight")
                        .HasColumnType("integer");

                    b.Property<string>("OriginalMetadata")
                        .HasColumnType("text");

                    b.Property<long?>("OriginalSizeBytes")
                        .HasColumnType("bigint");

                    b.Property<int?>("OriginalWidth")
                        .HasColumnType("integer");

                    b.Property<string>("OutputStoragePath")
                        .HasColumnType("text");

                    b.Property<string>("Quality")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TranscoderJobName")
                        .HasColumnType("text");

                    b.Property<bool>("UploadToGooglePhotos")
                        .HasColumnType("boolean");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Status");

                    b.HasIndex("UserId");

                    b.ToTable("CompressionJobs");
                });

            modelBuilder.Entity("VidCompressor.Models.CreditCost", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("Cost")
                        .HasColumnType("integer");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<string>("OperationType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("IsActive");

                    b.HasIndex("OperationType");

                    b.ToTable("CreditCosts");
                });

            modelBuilder.Entity("VidCompressor.Models.CreditTransaction", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("Amount")
                        .HasColumnType("integer");

                    b.Property<int>("BalanceAfter")
                        .HasColumnType("integer");

                    b.Property<string>("CompressionJobId")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PaymentId")
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CompressionJobId");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Type");

                    b.HasIndex("UserId");

                    b.ToTable("CreditTransactions");
                });

            modelBuilder.Entity("VidCompressor.Models.User", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<int>("Credits")
                        .HasColumnType("integer");

                    b.Property<bool>("DefaultUploadToGooglePhotos")
                        .HasColumnType("boolean");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("GoogleAccessToken")
                        .HasColumnType("text");

                    b.Property<string>("GoogleRefreshToken")
                        .HasColumnType("text");

                    b.Property<DateTime?>("GoogleTokenExpiry")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("SubscriptionStatus")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("VidCompressor.Models.EmailSignup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("IpAddress")
                        .HasColumnType("text");

                    b.Property<bool>("IsNotified")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("NotifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UserAgent")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.ToTable("EmailSignups");
                });

            modelBuilder.Entity("VidCompressor.Models.UserMediaItem", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("text");

                    b.Property<DateTime>("AddedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("BaseUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("CreationTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("FileSizeBytes")
                        .HasColumnType("bigint");

                    b.Property<string>("Filename")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("GoogleMediaItemId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("GooglePhotosUrl")
                        .HasColumnType("text");

                    b.Property<int?>("Height")
                        .HasColumnType("integer");

                    b.Property<DateTime>("LastAccessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("MediaType")
                        .HasColumnType("integer");

                    b.Property<string>("Metadata")
                        .HasColumnType("text");

                    b.Property<string>("MimeType")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("Width")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AddedAt");

                    b.HasIndex("GoogleMediaItemId");

                    b.HasIndex("LastAccessedAt");

                    b.HasIndex("UserId");

                    b.HasIndex("UserId", "GoogleMediaItemId")
                        .IsUnique();

                    b.ToTable("UserMediaItems");
                });

            modelBuilder.Entity("VidCompressor.Models.CompressionJob", b =>
                {
                    b.HasOne("VidCompressor.Models.User", "User")
                        .WithMany("CompressionJobs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("VidCompressor.Models.CreditTransaction", b =>
                {
                    b.HasOne("VidCompressor.Models.CompressionJob", "CompressionJob")
                        .WithMany()
                        .HasForeignKey("CompressionJobId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("VidCompressor.Models.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CompressionJob");

                    b.Navigation("User");
                });

            modelBuilder.Entity("VidCompressor.Models.UserMediaItem", b =>
                {
                    b.HasOne("VidCompressor.Models.User", "User")
                        .WithMany("MediaItems")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("VidCompressor.Models.User", b =>
                {
                    b.Navigation("CompressionJobs");

                    b.Navigation("MediaItems");
                });
#pragma warning restore 612, 618
        }
    }
}
