﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace backend.Migrations
{
    /// <inheritdoc />
    public partial class AddUserMediaItems : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "UserMediaItems",
                columns: table => new
                {
                    Id = table.Column<string>(type: "text", nullable: false),
                    UserId = table.Column<string>(type: "text", nullable: false),
                    GoogleMediaItemId = table.Column<string>(type: "text", nullable: false),
                    Filename = table.Column<string>(type: "text", nullable: false),
                    MimeType = table.Column<string>(type: "text", nullable: false),
                    BaseUrl = table.Column<string>(type: "text", nullable: false),
                    MediaType = table.Column<int>(type: "integer", nullable: false),
                    Width = table.Column<int>(type: "integer", nullable: true),
                    Height = table.Column<int>(type: "integer", nullable: true),
                    CreationTime = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Metadata = table.Column<string>(type: "text", nullable: true),
                    AddedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastAccessedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserMediaItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_UserMediaItems_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserMediaItems_AddedAt",
                table: "UserMediaItems",
                column: "AddedAt");

            migrationBuilder.CreateIndex(
                name: "IX_UserMediaItems_GoogleMediaItemId",
                table: "UserMediaItems",
                column: "GoogleMediaItemId");

            migrationBuilder.CreateIndex(
                name: "IX_UserMediaItems_LastAccessedAt",
                table: "UserMediaItems",
                column: "LastAccessedAt");

            migrationBuilder.CreateIndex(
                name: "IX_UserMediaItems_UserId",
                table: "UserMediaItems",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserMediaItems_UserId_GoogleMediaItemId",
                table: "UserMediaItems",
                columns: new[] { "UserId", "GoogleMediaItemId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UserMediaItems");
        }
    }
}
