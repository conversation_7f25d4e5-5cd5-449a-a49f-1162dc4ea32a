﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace backend.Migrations
{
    /// <inheritdoc />
    public partial class RemoveOverwriteOriginalFeature : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DefaultOverwriteOriginal",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "OverwriteOriginal",
                table: "CompressionJobs");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "DefaultOverwriteOriginal",
                table: "Users",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "OverwriteOriginal",
                table: "CompressionJobs",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }
    }
}
