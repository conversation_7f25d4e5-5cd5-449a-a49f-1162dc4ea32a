# Video Compression Workflow

## 🎯 Overview

The video compression workflow has one main option for handling compressed videos:

1. **`UploadToGooglePhotos`** - Whether to upload the compressed video back to Google Photos

**Note:** The original "overwrite/delete original" feature has been removed due to Google Photos API limitations. The Google Photos Library API does not support deleting media items from a user's library.

## 📋 Option Combinations

| UploadToGooglePhotos | Result |
|---------------------|---------|
| ✅ `true` | Upload compressed video, keep original (default) |
| ❌ `false` | Keep compressed video in cloud storage only |

## 🔄 Updated Workflow

### **1. User Request**
```json
{
  "quality": "720p",
  "uploadToGooglePhotos": true    // Default: true
}
```

### **2. Job Processing Steps**

#### **Always Executed:**
1. `Queued` → Download from Google Photos
2. `DownloadingFromGooglePhotos` → Upload to Cloud Storage
3. `UploadingToStorage` → Start transcoding
4. `TranscodingInProgress` → Wait for completion
5. `DownloadingFromStorage` → Download compressed video

#### **Conditional Steps:**

**If `UploadToGooglePhotos = true`:**
6. `UploadingToGooglePhotos` → Upload to Google Photos

**Always:**
7. `Completed` → Job finished

### **3. Status Flow Examples**

#### **Default Behavior (Upload to Google Photos):**
```
Queued → DownloadingFromGooglePhotos → UploadingToStorage →
TranscodingInProgress → DownloadingFromStorage →
UploadingToGooglePhotos → Completed
```

#### **Cloud Storage Only:**
```
Queued → DownloadingFromGooglePhotos → UploadingToStorage →
TranscodingInProgress → DownloadingFromStorage → Completed
```

## 🗄️ Database Schema

### **CompressionJob Model:**
```csharp
public class CompressionJob
{
    // ... other properties ...

    /// <summary>
    /// Whether to upload the compressed video back to Google Photos
    /// </summary>
    public bool UploadToGooglePhotos { get; set; } = true;
}
```

### **User Preferences:**
```csharp
public class User
{
    // ... other properties ...

    /// <summary>
    /// Default setting for whether to upload compressed videos back to Google Photos
    /// </summary>
    public bool DefaultUploadToGooglePhotos { get; set; } = true;
}
```

## 🎛️ Frontend UI Implications

### **Compression Options UI:**
```
┌─ Compression Settings ─────────────────┐
│                                        │
│ Quality: [720p ▼]                      │
│                                        │
│ ☑️ Upload compressed video to Google    │
│    Photos                              │
│                                        │
│ ☐ Replace original video               │
│   (only if uploading to Google Photos) │
│                                        │
│ [Compress Videos]                      │
└────────────────────────────────────────┘
```

### **Logic:**
- Default values come from user preferences
- Clear messaging about what the upload option does

## 🔧 Implementation Notes

### **Backend Logic:**
1. Use user's default preferences when creating new jobs
2. Update job status appropriately based on selected options
3. Skip upload step when not needed

### **Error Handling:**
- Provide clear error messages for each step
- Allow retry of individual steps

### **API Limitations:**
- The Google Photos Library API does not support deleting media items from a user's library
- Only app-created content can be modified (descriptions) or removed from app-created albums
- This is why the "overwrite original" feature was removed from the application

### **Performance:**
- Skip unnecessary steps to reduce processing time
- Clean up cloud storage files after successful completion
- Implement proper timeout handling for each step

## 📊 Benefits

1. **Flexibility**: Users can choose exactly what they want
2. **Safety**: Default behavior keeps originals safe
3. **Efficiency**: Skip unnecessary upload steps when not needed
4. **Clear Intent**: Separate concerns make the workflow easier to understand
5. **User Control**: Granular control over the compression process
