using System.ComponentModel.DataAnnotations;

namespace VidCompressor.Models
{
    public class EmailSignup
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [EmailAddress]
        [MaxLength(255)]
        public string Email { get; set; } = string.Empty;

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public string? IpAddress { get; set; }

        public string? UserAgent { get; set; }

        public bool IsNotified { get; set; } = false;

        public DateTime? NotifiedAt { get; set; }
    }

    public class EmailSignupRequest
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
    }

    public class EmailSignupResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
    }
}
