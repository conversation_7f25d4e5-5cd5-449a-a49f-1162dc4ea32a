using System.Text.Json.Serialization;

namespace VidCompressor.Models;

public class GoogleTokenResponse
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; } = string.Empty;

    [JsonPropertyName("refresh_token")]
    public string RefreshToken { get; set; } = string.Empty;

    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }

    [JsonPropertyName("token_type")]
    public string TokenType { get; set; } = string.Empty;

    [JsonPropertyName("scope")]
    public string Scope { get; set; } = string.Empty;

    [JsonPropertyName("id_token")]
    public string IdToken { get; set; } = string.Empty;
}

public class GoogleUserInfo
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Picture { get; set; } = string.Empty;
}

public class PickerSessionResponse
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("pickerUri")]
    public string PickerUri { get; set; } = string.Empty;

    [JsonPropertyName("pollingConfig")]
    public PollingConfig? PollingConfig { get; set; }

    [JsonPropertyName("mediaItemsSet")]
    public bool MediaItemsSet { get; set; }
}

public class PollingConfig
{
    [JsonPropertyName("pollInterval")]
    public string PollInterval { get; set; } = string.Empty;

    [JsonPropertyName("timeoutIn")]
    public string TimeoutIn { get; set; } = string.Empty;
}

public class PickedMediaItem
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("createTime")]
    public string CreateTime { get; set; } = string.Empty;

    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    [JsonPropertyName("mediaFile")]
    public MediaFile? MediaFile { get; set; }

    [JsonPropertyName("productUrl")]
    public string? ProductUrl { get; set; }
}

public class MediaFile
{
    [JsonPropertyName("baseUrl")]
    public string BaseUrl { get; set; } = string.Empty;

    [JsonPropertyName("mimeType")]
    public string MimeType { get; set; } = string.Empty;

    [JsonPropertyName("filename")]
    public string Filename { get; set; } = string.Empty;

    [JsonPropertyName("mediaFileMetadata")]
    public MediaFileMetadata? MediaFileMetadata { get; set; }
}

public class MediaFileMetadata
{
    [JsonPropertyName("width")]
    public int Width { get; set; }

    [JsonPropertyName("height")]
    public int Height { get; set; }

    [JsonPropertyName("cameraMake")]
    public string CameraMake { get; set; } = string.Empty;

    [JsonPropertyName("cameraModel")]
    public string CameraModel { get; set; } = string.Empty;

    [JsonPropertyName("photoMetadata")]
    public PhotoMetadata? PhotoMetadata { get; set; }

    [JsonPropertyName("videoMetadata")]
    public VideoMetadata? VideoMetadata { get; set; }
}

public class PhotoMetadata
{
    [JsonPropertyName("focalLength")]
    public double FocalLength { get; set; }

    [JsonPropertyName("apertureFNumber")]
    public double ApertureFNumber { get; set; }

    [JsonPropertyName("isoEquivalent")]
    public int IsoEquivalent { get; set; }

    [JsonPropertyName("exposureTime")]
    public string ExposureTime { get; set; } = string.Empty;
}

public class VideoMetadata
{
    [JsonPropertyName("fps")]
    public double Fps { get; set; }

    [JsonPropertyName("processingStatus")]
    public string ProcessingStatus { get; set; } = string.Empty;
}

public class ListMediaItemsResponse
{
    [JsonPropertyName("mediaItems")]
    public List<PickedMediaItem> MediaItems { get; set; } = new();

    [JsonPropertyName("nextPageToken")]
    public string? NextPageToken { get; set; }
}
