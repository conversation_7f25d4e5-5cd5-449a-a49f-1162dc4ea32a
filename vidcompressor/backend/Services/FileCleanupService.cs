using Microsoft.EntityFrameworkCore;
using VidCompressor.Models;

namespace VidCompressor.Services;

/// <summary>
/// Background service that cleans up old compressed files and storage objects
/// </summary>
public class FileCleanupService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<FileCleanupService> _logger;
    private readonly TimeSpan _cleanupInterval = TimeSpan.FromHours(6); // Run every 6 hours
    private readonly TimeSpan _fileRetentionPeriod = TimeSpan.FromDays(7); // Keep files for 7 days

    public FileCleanupService(IServiceProvider serviceProvider, ILogger<FileCleanupService> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("File cleanup service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CleanupOldFiles();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during file cleanup");
            }

            await Task.Delay(_cleanupInterval, stoppingToken);
        }
    }

    private async Task CleanupOldFiles()
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var storageService = scope.ServiceProvider.GetRequiredService<GoogleCloudStorageService>();

        var cutoffDate = DateTime.UtcNow - _fileRetentionPeriod;

        _logger.LogInformation("Starting cleanup of files older than {CutoffDate}", cutoffDate);

        // Find completed jobs older than retention period
        var oldJobs = await context.CompressionJobs
            .Where(j => j.CompletedAt.HasValue && j.CompletedAt.Value < cutoffDate)
            .Where(j => !string.IsNullOrEmpty(j.CompressedFilePath) || !string.IsNullOrEmpty(j.OutputStoragePath))
            .ToListAsync();

        var cleanedLocalFiles = 0;
        var cleanedStorageFiles = 0;

        foreach (var job in oldJobs)
        {
            // Clean up local compressed files (temp files for batch upload)
            if (!string.IsNullOrEmpty(job.CompressedFilePath) && File.Exists(job.CompressedFilePath))
            {
                try
                {
                    File.Delete(job.CompressedFilePath);
                    job.CompressedFilePath = null;
                    cleanedLocalFiles++;
                    _logger.LogDebug("Deleted old local temp file for job {JobId}", job.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete old local temp file {FilePath} for job {JobId}",
                        job.CompressedFilePath, job.Id);
                }
            }

            // Clean up cloud storage files (compressed files for downloads)
            if (!string.IsNullOrEmpty(job.OutputStoragePath))
            {
                try
                {
                    await storageService.DeleteFileAsync(job.OutputStoragePath);
                    job.OutputStoragePath = null;
                    cleanedStorageFiles++;
                    _logger.LogDebug("Deleted old cloud storage file for job {JobId}", job.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete old cloud storage file {StoragePath} for job {JobId}",
                        job.OutputStoragePath, job.Id);
                }
            }
        }

        if (cleanedLocalFiles > 0 || cleanedStorageFiles > 0)
        {
            await context.SaveChangesAsync();
            _logger.LogInformation("Cleanup completed: {LocalFiles} local files, {StorageFiles} storage files deleted", 
                cleanedLocalFiles, cleanedStorageFiles);
        }
        else
        {
            _logger.LogDebug("No old files found for cleanup");
        }
    }
}
