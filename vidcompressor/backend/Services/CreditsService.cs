using Microsoft.EntityFrameworkCore;
using VidCompressor.Models;

namespace VidCompressor.Services;

/// <summary>
/// Service for managing user credits, calculating costs, and processing transactions
/// </summary>
public class CreditsService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<CreditsService> _logger;

    public CreditsService(ApplicationDbContext context, ILogger<CreditsService> logger)
    {
        _context = context;
        _logger = logger;
    }

    /// <summary>
    /// Calculate credit cost for a compression operation
    /// </summary>
    /// <param name="mediaType">Type of media (Photo or Video)</param>
    /// <param name="quality">Quality setting (low, medium, high)</param>
    /// <param name="durationMinutes">Duration in minutes (for videos only)</param>
    /// <returns>Credit cost for the operation</returns>
    public async Task<int> CalculateCreditCostAsync(MediaType mediaType, string quality, double? durationMinutes = null)
    {
        if (mediaType == MediaType.Photo)
        {
            var photoCost = await GetCreditCostAsync(CreditCostOperationTypes.PhotoCompression);
            return photoCost?.Cost ?? 2; // Default fallback
        }

        // For videos, determine operation type based on quality
        string operationType = quality.ToLower() switch
        {
            "low" => CreditCostOperationTypes.VideoCompressionSD,
            "medium" => CreditCostOperationTypes.VideoCompressionHD,
            "high" => CreditCostOperationTypes.VideoCompression4K,
            _ => CreditCostOperationTypes.VideoCompressionHD // Default to HD
        };

        var videoCost = await GetCreditCostAsync(operationType);
        var costPerMinute = videoCost?.Cost ?? GetDefaultVideoCost(quality);
        
        // Calculate total cost based on duration
        var totalMinutes = Math.Max(1, Math.Ceiling(durationMinutes ?? 1)); // Minimum 1 minute
        return (int)(costPerMinute * totalMinutes);
    }

    /// <summary>
    /// Get credit cost configuration for an operation type
    /// </summary>
    private async Task<CreditCost?> GetCreditCostAsync(string operationType)
    {
        return await _context.CreditCosts
            .Where(cc => cc.OperationType == operationType && cc.IsActive)
            .FirstOrDefaultAsync();
    }

    /// <summary>
    /// Get default video cost if database configuration is not available
    /// </summary>
    private int GetDefaultVideoCost(string quality)
    {
        return quality.ToLower() switch
        {
            "low" => 6,    // SD
            "medium" => 12, // HD
            "high" => 25,   // 4K
            _ => 12         // Default to HD
        };
    }

    /// <summary>
    /// Check if user has sufficient credits for an operation
    /// </summary>
    public async Task<bool> HasSufficientCreditsAsync(string userId, int requiredCredits)
    {
        var user = await _context.Users.FindAsync(userId);
        return user != null && user.Credits >= requiredCredits;
    }

    /// <summary>
    /// Get user's current credit balance
    /// </summary>
    public async Task<int> GetUserCreditsAsync(string userId)
    {
        var user = await _context.Users.FindAsync(userId);
        return user?.Credits ?? 0;
    }

    /// <summary>
    /// Deduct credits from user account and create transaction record
    /// </summary>
    public async Task<bool> DeductCreditsAsync(string userId, int amount, string description, string? compressionJobId = null)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null || user.Credits < amount)
            {
                return false;
            }

            user.Credits -= amount;
            
            var creditTransaction = new CreditTransaction
            {
                UserId = userId,
                Type = CreditTransactionType.Usage,
                Amount = -amount, // Negative for usage
                BalanceAfter = user.Credits,
                Description = description,
                CompressionJobId = compressionJobId
            };

            _context.CreditTransactions.Add(creditTransaction);
            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogInformation("Deducted {Amount} credits from user {UserId}. New balance: {Balance}", 
                amount, userId, user.Credits);
            
            return true;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Failed to deduct credits for user {UserId}", userId);
            return false;
        }
    }

    /// <summary>
    /// Add credits to user account and create transaction record
    /// </summary>
    public async Task<bool> AddCreditsAsync(string userId, int amount, CreditTransactionType type, string description, string? paymentId = null)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        
        try
        {
            var user = await _context.Users.FindAsync(userId);
            if (user == null)
            {
                return false;
            }

            user.Credits += amount;
            
            var creditTransaction = new CreditTransaction
            {
                UserId = userId,
                Type = type,
                Amount = amount, // Positive for additions
                BalanceAfter = user.Credits,
                Description = description,
                PaymentId = paymentId
            };

            _context.CreditTransactions.Add(creditTransaction);
            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogInformation("Added {Amount} credits to user {UserId}. New balance: {Balance}", 
                amount, userId, user.Credits);
            
            return true;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Failed to add credits for user {UserId}", userId);
            return false;
        }
    }

    /// <summary>
    /// Refund credits for a failed or cancelled job
    /// </summary>
    public async Task<bool> RefundCreditsAsync(string userId, int amount, string description, string? compressionJobId = null)
    {
        return await AddCreditsAsync(userId, amount, CreditTransactionType.Refund, description);
    }

    /// <summary>
    /// Get user's credit transaction history
    /// </summary>
    public async Task<List<CreditTransaction>> GetUserTransactionHistoryAsync(string userId, int limit = 50)
    {
        return await _context.CreditTransactions
            .Where(ct => ct.UserId == userId)
            .OrderByDescending(ct => ct.CreatedAt)
            .Take(limit)
            .ToListAsync();
    }

    /// <summary>
    /// Get all active credit costs
    /// </summary>
    public async Task<List<CreditCost>> GetActiveCreditCostsAsync()
    {
        return await _context.CreditCosts
            .Where(cc => cc.IsActive)
            .OrderBy(cc => cc.OperationType)
            .ToListAsync();
    }

    /// <summary>
    /// Estimate credit cost for a media item
    /// </summary>
    public async Task<CreditCostEstimate> EstimateCreditCostAsync(MediaType mediaType, string quality, double? durationMinutes = null, long? fileSizeBytes = null)
    {
        var cost = await CalculateCreditCostAsync(mediaType, quality, durationMinutes);
        
        return new CreditCostEstimate
        {
            MediaType = mediaType,
            Quality = quality,
            DurationMinutes = durationMinutes,
            FileSizeBytes = fileSizeBytes,
            EstimatedCredits = cost,
            Description = GetCostDescription(mediaType, quality, durationMinutes, cost)
        };
    }

    private string GetCostDescription(MediaType mediaType, string quality, double? durationMinutes, int cost)
    {
        if (mediaType == MediaType.Photo)
        {
            return $"{cost} credits for photo compression";
        }

        var qualityText = quality.ToLower() switch
        {
            "low" => "SD",
            "medium" => "HD", 
            "high" => "4K",
            _ => quality
        };

        var duration = durationMinutes.HasValue ? $" ({Math.Ceiling(durationMinutes.Value)} min)" : "";
        return $"{cost} credits for {qualityText} video compression{duration}";
    }
}

/// <summary>
/// Credit cost estimation result
/// </summary>
public class CreditCostEstimate
{
    public MediaType MediaType { get; set; }
    public string Quality { get; set; } = string.Empty;
    public double? DurationMinutes { get; set; }
    public long? FileSizeBytes { get; set; }
    public int EstimatedCredits { get; set; }
    public string Description { get; set; } = string.Empty;
}
