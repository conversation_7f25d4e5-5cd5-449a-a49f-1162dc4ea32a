{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=vidcompressor;Username=vidcompressor;Password=password"}, "Google": {"ClientId": "************-oudag9d2btbee2n0m3ulh9c9pa5dr7fq.apps.googleusercontent.com", "ClientSecret": "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"}, "GoogleCloud": {"ProjectId": "YOUR_PROJECT_ID", "Region": "us-central1", "InputBucketName": "YOUR_PROJECT_ID-vidcompressor-input", "OutputBucketName": "YOUR_PROJECT_ID-vidcompressor-output", "ServiceAccountKeyPath": "/secrets/transcoder-service-account-key", "Transcoder": {"Location": "us-central1"}, "CloudTasks": {"ProjectId": "YOUR_PROJECT_ID", "Location": "us-central1", "QueueName": "video-compression-jobs", "HandlerUrl": "https://your-cloud-run-url"}}, "Jwt": {"Secret": "a_super_secret_and_long_key_for_jwt_validation"}}