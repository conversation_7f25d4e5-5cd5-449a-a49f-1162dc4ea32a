### Test Token Scopes
### Use this to check what scopes your current access token has

@access_token = ******************************************************************************************************************************************************************************************************************************

### 1. Check token info (what scopes does it have?)
GET https://www.googleapis.com/oauth2/v1/tokeninfo?access_token={{access_token}}

### 2. Test Photos Library API access
GET https://photoslibrary.googleapis.com/v1/mediaItems?pageSize=1
Authorization: Bearer {{access_token}}

### 3. Test specific media item access (replace with your media item ID)
GET https://photoslibrary.googleapis.com/v1/mediaItems/ACgWe6Nz79_gorNx8ZHh3J-_1sBnSWBrERt1ahI7cg9-NKu4RwmMoMmoUio7bC7KSJkPtvB3WI3jj-FsDhTw_Qq1wrOrlKxSJw
Authorization: Bearer {{access_token}}
