using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using VidCompressor.Models;
using VidCompressor.Services;


namespace VidCompressor.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class MediaController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly CloudTasksService _cloudTasksService;
    private readonly CreditsService _creditsService;
    private readonly ILogger<MediaController> _logger;

    public MediaController(
        ApplicationDbContext context,
        CloudTasksService cloudTasksService,
        CreditsService creditsService,
        ILogger<MediaController> logger)
    {
        _context = context;
        _cloudTasksService = cloudTasksService;
        _creditsService = creditsService;
        _logger = logger;
    }

    /// <summary>
    /// Initiates media compression (photos or videos) using appropriate compression service
    /// </summary>
    [HttpPost("{mediaItemId}/compress")]
    public async Task<IActionResult> CompressMedia(string mediaItemId, [FromBody] CompressionJobRequest request)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        _logger.LogInformation("Received compression request for media item {MediaItemId}, MediaType: {MediaType}, Quality: {Quality}, Filename: {Filename}",
            mediaItemId, request?.MediaType, request?.Quality, request?.Filename);

        if (request == null)
        {
            return BadRequest(new { error = "Request body is required" });
        }

        try
        {
            // Calculate credit cost for this operation
            double? durationMinutes = null;
            if (request.MediaType == MediaType.Video)
            {
                // Default to 1 minute for videos since duration parsing from metadata is complex
                durationMinutes = 1.0;
                _logger.LogInformation("Using default 1 minute duration for video {MediaItemId} credit calculation", mediaItemId);
            }

            var creditCost = await _creditsService.CalculateCreditCostAsync(
                request.MediaType,
                request.Quality,
                durationMinutes);

            // Check if user has sufficient credits
            var hasSufficientCredits = await _creditsService.HasSufficientCreditsAsync(userId, creditCost);
            if (!hasSufficientCredits)
            {
                var currentBalance = await _creditsService.GetUserCreditsAsync(userId);
                return BadRequest(new {
                    error = "Insufficient credits",
                    required = creditCost,
                    current = currentBalance,
                    shortfall = creditCost - currentBalance
                });
            }

            // Create compression job record
            var compressionJob = new CompressionJob
            {
                UserId = userId,
                MediaItemId = mediaItemId,
                MediaType = request.MediaType,
                Quality = request.Quality,
                UploadToGooglePhotos = request.UploadToGooglePhotos,
                BaseUrl = request.BaseUrl,
                OriginalWidth = request.OriginalWidth,
                OriginalHeight = request.OriginalHeight,
                OriginalFilename = request.Filename,
                GooglePhotosUrl = request.GooglePhotosUrl,
                CreditsUsed = creditCost,
                Status = CompressionJobStatus.Queued
            };

            _context.CompressionJobs.Add(compressionJob);
            await _context.SaveChangesAsync();

            // Deduct credits from user account
            var mediaTypeText = request.MediaType == MediaType.Photo ? "photo" : "video";
            var deductionSuccess = await _creditsService.DeductCreditsAsync(
                userId,
                creditCost,
                $"{char.ToUpper(mediaTypeText[0])}{mediaTypeText[1..]} compression ({request.Quality} quality)",
                compressionJob.Id);

            if (!deductionSuccess)
            {
                // If credit deduction fails, mark job as failed and don't process
                compressionJob.Status = CompressionJobStatus.Failed;
                compressionJob.ErrorMessage = "Failed to deduct credits";
                await _context.SaveChangesAsync();

                return BadRequest(new { error = "Failed to deduct credits for compression" });
            }

            // Send initial status update via SignalR
            try
            {
                var hubContext = HttpContext.RequestServices.GetRequiredService<IHubContext<VidCompressor.Hubs.NotificationHub>>();

                var statusUpdate = new
                {
                    jobId = compressionJob.Id,
                    mediaItemId = compressionJob.MediaItemId,
                    status = "Queued",
                    message = "Job queued for processing",
                    progress = 0,
                    userId = userId
                };

                var jobListUpdate = new
                {
                    action = "created",
                    jobId = compressionJob.Id,
                    userId = userId
                };

                // Send to all clients and specific user group
                await hubContext.Clients.All.SendAsync("CompressionStatusUpdate", statusUpdate);
                await hubContext.Clients.Group($"User_{userId}").SendAsync("CompressionStatusUpdate", statusUpdate);
                await hubContext.Clients.Group("AllUsers").SendAsync("CompressionStatusUpdate", statusUpdate);

                await hubContext.Clients.All.SendAsync("JobListUpdate", jobListUpdate);
                await hubContext.Clients.Group($"User_{userId}").SendAsync("JobListUpdate", jobListUpdate);
                await hubContext.Clients.Group("AllUsers").SendAsync("JobListUpdate", jobListUpdate);

                _logger.LogInformation("Sent initial SignalR updates for job {JobId} to user {UserId}", compressionJob.Id, userId);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to send initial SignalR updates for job {JobId}", compressionJob.Id);
            }

            // Queue the job for background processing
            await _cloudTasksService.EnqueueCompressionJobAsync(compressionJob.Id);

            return Ok(new CompressionJobResponse
            {
                JobId = compressionJob.Id,
                Status = compressionJob.Status.ToString(),
                Message = $"{char.ToUpper(mediaTypeText[0])}{mediaTypeText[1..]} compression job has been queued",
                CreatedAt = compressionJob.CreatedAt
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to queue compression job for media item {MediaItemId}", mediaItemId);
            return StatusCode(500, new { error = "Failed to queue compression job", details = ex.Message });
        }
    }

    /// <summary>
    /// Gets the status of a compression job
    /// </summary>
    [HttpGet("jobs/{jobId}/status")]
    public async Task<IActionResult> GetCompressionJobStatus(string jobId)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        var job = await _context.CompressionJobs
            .Where(j => j.Id == jobId && j.UserId == userId)
            .FirstOrDefaultAsync();

        if (job == null)
        {
            return NotFound(new { message = "Compression job not found" });
        }

        return Ok(new CompressionJobResponse
        {
            JobId = job.Id,
            Status = job.Status.ToString(),
            Message = GetStatusMessage(job.Status),
            CreatedAt = job.CreatedAt,
            CompletedAt = job.CompletedAt,
            CompressionRatio = job.CompressionRatio,
            ErrorMessage = job.ErrorMessage
        });
    }

    /// <summary>
    /// Gets all compression jobs for the current user
    /// </summary>
    [HttpGet("jobs")]
    public async Task<IActionResult> GetCompressionJobs()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        var jobs = await _context.CompressionJobs
            .Where(j => j.UserId == userId)
            .OrderByDescending(j => j.CreatedAt)
            .Take(50) // Limit to last 50 jobs
            .Select(j => new
            {
                jobId = j.Id,
                mediaItemId = j.MediaItemId,
                filename = j.OriginalFilename ?? $"{(j.MediaType == MediaType.Video ? "Video" : "Photo")}_{j.MediaItemId.Substring(Math.Max(0, j.MediaItemId.Length - 8))}",
                mimeType = j.MediaType == MediaType.Video ? "video/mp4" : "image/jpeg",
                baseUrl = j.BaseUrl,
                status = j.Status.ToString(),
                message = GetStatusMessage(j.Status),
                progress = GetProgressPercentage(j.Status),
                quality = j.Quality,
                uploadToGooglePhotos = j.UploadToGooglePhotos,
                createdAt = j.CreatedAt.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                completedAt = j.CompletedAt.HasValue ? j.CompletedAt.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") : null,
                compressionRatio = j.CompressionRatio,
                error = j.ErrorMessage,
                mediaType = j.MediaType == MediaType.Video ? "Video" : "Photo",
                googlePhotosUrl = j.GooglePhotosUrl,
                compressedGooglePhotosUrl = j.CompressedGooglePhotosUrl,
                creditsUsed = j.CreditsUsed
            })
            .ToListAsync();

        return Ok(jobs);
    }

    /// <summary>
    /// Deletes a compression job
    /// </summary>
    [HttpDelete("jobs/{jobId}")]
    public async Task<IActionResult> DeleteCompressionJob(string jobId)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        var job = await _context.CompressionJobs
            .Where(j => j.Id == jobId && j.UserId == userId)
            .FirstOrDefaultAsync();

        if (job == null)
        {
            return NotFound(new { message = "Compression job not found" });
        }

        // Only allow deletion of completed, failed, or cancelled jobs
        if (job.Status == CompressionJobStatus.DownloadingFromGooglePhotos ||
            job.Status == CompressionJobStatus.UploadingToStorage ||
            job.Status == CompressionJobStatus.TranscodingInProgress ||
            job.Status == CompressionJobStatus.DownloadingFromStorage ||
            job.Status == CompressionJobStatus.UploadingToGooglePhotos)
        {
            return BadRequest(new { message = "Cannot delete an active job. Please wait for it to complete or cancel it first." });
        }

        _context.CompressionJobs.Remove(job);
        await _context.SaveChangesAsync();

        // Send job list update notification via SignalR
        try
        {
            var hubContext = HttpContext.RequestServices.GetRequiredService<IHubContext<VidCompressor.Hubs.NotificationHub>>();
            await hubContext.Clients.All.SendAsync("JobListUpdate", new
            {
                action = "deleted",
                jobId = jobId,
                userId = userId
            });
            _logger.LogInformation("Sent job deletion SignalR update for job {JobId}", jobId);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to send job deletion SignalR update for job {JobId}", jobId);
        }

        return Ok(new { message = "Compression job deleted successfully" });
    }

    /// <summary>
    /// Deletes all completed, failed, or cancelled compression jobs for the current user
    /// </summary>
    [HttpDelete("jobs")]
    public async Task<IActionResult> DeleteAllCompressionJobs()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        var deletableJobs = await _context.CompressionJobs
            .Where(j => j.UserId == userId &&
                       (j.Status == CompressionJobStatus.Completed ||
                        j.Status == CompressionJobStatus.Failed ||
                        j.Status == CompressionJobStatus.Cancelled))
            .ToListAsync();

        if (!deletableJobs.Any())
        {
            return Ok(new { message = "No jobs to delete", deletedCount = 0 });
        }

        _context.CompressionJobs.RemoveRange(deletableJobs);
        await _context.SaveChangesAsync();

        // Send job list update notification via SignalR for bulk deletion
        try
        {
            var hubContext = HttpContext.RequestServices.GetRequiredService<IHubContext<VidCompressor.Hubs.NotificationHub>>();
            await hubContext.Clients.All.SendAsync("JobListUpdate", new
            {
                action = "bulk_deleted",
                deletedCount = deletableJobs.Count,
                userId = userId
            });
            _logger.LogInformation("Sent bulk job deletion SignalR update for {Count} jobs", deletableJobs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to send bulk job deletion SignalR update");
        }

        return Ok(new { message = $"Deleted {deletableJobs.Count} compression jobs", deletedCount = deletableJobs.Count });
    }

    /// <summary>
    /// Downloads the compressed file for a completed compression job
    /// </summary>
    [HttpGet("jobs/{jobId}/download")]
    public async Task<IActionResult> DownloadCompressedFile(string jobId)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        var job = await _context.CompressionJobs
            .Where(j => j.Id == jobId && j.UserId == userId)
            .FirstOrDefaultAsync();

        if (job == null)
        {
            return NotFound(new { message = "Compression job not found" });
        }

        if (job.Status != CompressionJobStatus.Completed)
        {
            return BadRequest(new { message = "Job is not completed yet" });
        }

        try
        {
            // Try cloud storage first (for both photos and videos)
            if (!string.IsNullOrEmpty(job.OutputStoragePath))
            {
                var storageService = HttpContext.RequestServices.GetRequiredService<GoogleCloudStorageService>();

                try
                {
                    using var compressedStream = await storageService.DownloadFromOutputBucketAsync(job.OutputStoragePath);
                    var memoryStream = new MemoryStream();
                    await compressedStream.CopyToAsync(memoryStream);
                    memoryStream.Position = 0;

                    var fileName = job.OriginalFilename ?? "compressed_media";
                    string mimeType;

                    if (job.MediaType == MediaType.Photo)
                    {
                        var extension = job.Quality.ToLower() == "low" ? ".webp" : ".jpg";
                        if (!fileName.EndsWith(extension))
                        {
                            fileName = Path.GetFileNameWithoutExtension(fileName) + extension;
                        }
                        mimeType = GetMimeType(extension);
                    }
                    else
                    {
                        if (!fileName.EndsWith(".mp4"))
                        {
                            fileName = Path.GetFileNameWithoutExtension(fileName) + ".mp4";
                        }
                        mimeType = "video/mp4";
                    }

                    return File(memoryStream.ToArray(), mimeType, fileName);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to download compressed file from cloud storage for job {JobId}", jobId);

                    // For photos, fallback to local file if cloud storage fails
                    if (job.MediaType == MediaType.Photo && !string.IsNullOrEmpty(job.CompressedFilePath))
                    {
                        _logger.LogInformation("Falling back to local file for photo job {JobId}", jobId);
                        // Continue to local file fallback below
                    }
                    else
                    {
                        return StatusCode(500, new { message = "Failed to download compressed file from cloud storage" });
                    }
                }
            }

            // Fallback: For photos, check if we have a local compressed file
            if (job.MediaType == MediaType.Photo && !string.IsNullOrEmpty(job.CompressedFilePath))
            {
                if (System.IO.File.Exists(job.CompressedFilePath))
                {
                    var fileBytes = await System.IO.File.ReadAllBytesAsync(job.CompressedFilePath);
                    var fileName = job.OriginalFilename ?? "compressed_photo";
                    var extension = job.Quality.ToLower() == "low" ? ".webp" : ".jpg";

                    if (!fileName.EndsWith(extension))
                    {
                        fileName = Path.GetFileNameWithoutExtension(fileName) + extension;
                    }

                    return File(fileBytes, GetMimeType(extension), fileName);
                }
            }

            return NotFound(new { message = "Compressed file not found" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading compressed file for job {JobId}", jobId);
            return StatusCode(500, new { message = "Error downloading compressed file" });
        }
    }

    /// <summary>
    /// Manually trigger cleanup of old compressed files (admin endpoint)
    /// </summary>
    [HttpPost("cleanup")]
    public async Task<IActionResult> CleanupOldFiles()
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        if (userId == null) return Unauthorized();

        try
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-7); // Files older than 7 days

            var oldJobs = await _context.CompressionJobs
                .Where(j => j.UserId == userId)
                .Where(j => j.CompletedAt.HasValue && j.CompletedAt.Value < cutoffDate)
                .Where(j => !string.IsNullOrEmpty(j.CompressedFilePath) || !string.IsNullOrEmpty(j.OutputStoragePath))
                .ToListAsync();

            var cleanedCount = 0;
            var storageService = HttpContext.RequestServices.GetRequiredService<GoogleCloudStorageService>();

            foreach (var job in oldJobs)
            {
                // Clean up local files
                if (!string.IsNullOrEmpty(job.CompressedFilePath) && System.IO.File.Exists(job.CompressedFilePath))
                {
                    try
                    {
                        System.IO.File.Delete(job.CompressedFilePath);
                        job.CompressedFilePath = null;
                        cleanedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to delete file {FilePath}", job.CompressedFilePath);
                    }
                }

                // Clean up storage files
                if (!string.IsNullOrEmpty(job.OutputStoragePath))
                {
                    try
                    {
                        await storageService.DeleteFileAsync(job.OutputStoragePath);
                        job.OutputStoragePath = null;
                        cleanedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to delete storage file {StoragePath}", job.OutputStoragePath);
                    }
                }
            }

            await _context.SaveChangesAsync();

            return Ok(new { message = $"Cleaned up {cleanedCount} old files", jobsProcessed = oldJobs.Count });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during manual cleanup");
            return StatusCode(500, new { message = "Error during cleanup" });
        }
    }

    private static string GetMimeType(string extension)
    {
        return extension.ToLower() switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".webp" => "image/webp",
            ".mp4" => "video/mp4",
            _ => "application/octet-stream"
        };
    }

    private static string GetStatusMessage(CompressionJobStatus status)
    {
        return status switch
        {
            CompressionJobStatus.Queued => "Queued for processing",
            CompressionJobStatus.DownloadingFromGooglePhotos => "Downloading from Google Photos",
            CompressionJobStatus.UploadingToStorage => "Uploading to cloud storage",
            CompressionJobStatus.TranscodingInProgress => "Video transcoding in progress",
            CompressionJobStatus.CompressingImage => "Image compression in progress",
            CompressionJobStatus.DownloadingFromStorage => "Downloading compressed media",
            CompressionJobStatus.ReadyForBatchUpload => "Ready for upload to Google Photos",
            CompressionJobStatus.UploadingToGooglePhotos => "Uploading to Google Photos",
            CompressionJobStatus.Completed => "Compression completed",
            CompressionJobStatus.Failed => "Compression failed",
            CompressionJobStatus.Cancelled => "Compression was cancelled",
            _ => "Unknown status"
        };
    }

    private static int GetProgressPercentage(CompressionJobStatus status)
    {
        return status switch
        {
            CompressionJobStatus.Queued => 0,
            CompressionJobStatus.DownloadingFromGooglePhotos => 10,
            CompressionJobStatus.UploadingToStorage => 20,
            CompressionJobStatus.TranscodingInProgress => 50,
            CompressionJobStatus.CompressingImage => 50,
            CompressionJobStatus.DownloadingFromStorage => 80,
            CompressionJobStatus.ReadyForBatchUpload => 90,
            CompressionJobStatus.UploadingToGooglePhotos => 95,
            CompressionJobStatus.Completed => 100,
            CompressionJobStatus.Failed => 0,
            CompressionJobStatus.Cancelled => 0,
            _ => 0
        };
    }
}
