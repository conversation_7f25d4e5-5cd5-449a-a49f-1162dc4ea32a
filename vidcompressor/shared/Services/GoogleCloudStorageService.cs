using Google.Cloud.Storage.V1;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using System.IO;
using System.Threading.Tasks;

namespace VidCompressor.Services;

public class GoogleCloudStorageService
{
    private readonly StorageClient _storageClient;
    private readonly GoogleCloudConfig _config;
    private readonly ILogger<GoogleCloudStorageService> _logger;

    public GoogleCloudStorageService(
        IOptions<GoogleCloudConfig> config,
        ILogger<GoogleCloudStorageService> logger)
    {
        _config = config.Value;
        _logger = logger;
        
        // Initialize the storage client with service account credentials
        _storageClient = StorageClient.Create();
    }

    /// <summary>
    /// Uploads a video stream to the input bucket
    /// </summary>
    /// <param name="videoStream">The video stream to upload</param>
    /// <param name="fileName">The name for the file in storage</param>
    /// <returns>The full path to the uploaded file</returns>
    public async Task<string> UploadToInputBucketAsync(Stream videoStream, string fileName)
    {
        try
        {
            var objectName = $"input/{DateTime.UtcNow:yyyy/MM/dd}/{fileName}";

            _logger.LogInformation("Uploading video to input bucket: {BucketName}/{ObjectName}",
                _config.InputBucketName, objectName);

            await _storageClient.UploadObjectAsync(_config.InputBucketName, objectName, "video/mp4", videoStream);

            var fullPath = $"gs://{_config.InputBucketName}/{objectName}";
            _logger.LogInformation("Successfully uploaded video to: {FullPath}", fullPath);

            return fullPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload video to input bucket");
            throw;
        }
    }

    /// <summary>
    /// Uploads a compressed media stream to the output bucket
    /// </summary>
    /// <param name="mediaStream">The compressed media stream to upload</param>
    /// <param name="fileName">The name for the file in storage</param>
    /// <returns>The full path to the uploaded file</returns>
    public async Task<string> UploadToOutputBucketAsync(Stream mediaStream, string fileName)
    {
        try
        {
            var objectName = $"output/{DateTime.UtcNow:yyyy/MM/dd}/{fileName}";

            _logger.LogInformation("Uploading compressed media to output bucket: {BucketName}/{ObjectName}",
                _config.OutputBucketName, objectName);

            await _storageClient.UploadObjectAsync(_config.OutputBucketName, objectName, null, mediaStream);

            var fullPath = $"gs://{_config.OutputBucketName}/{objectName}";
            _logger.LogInformation("Successfully uploaded compressed media: {FullPath}", fullPath);

            return fullPath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to upload compressed media to output bucket: {FileName}", fileName);
            throw;
        }
    }

    /// <summary>
    /// Downloads a video from the output bucket
    /// </summary>
    /// <param name="objectPath">The full GCS path (gs://bucket/object)</param>
    /// <returns>A stream containing the video data</returns>
    public async Task<Stream> DownloadFromOutputBucketAsync(string objectPath)
    {
        try
        {
            var (bucketName, objectName) = ParseGcsPath(objectPath);
            
            _logger.LogInformation("Downloading video from output bucket: {BucketName}/{ObjectName}", 
                bucketName, objectName);

            var memoryStream = new MemoryStream();
            await _storageClient.DownloadObjectAsync(bucketName, objectName, memoryStream);
            memoryStream.Position = 0;
            
            _logger.LogInformation("Successfully downloaded video from: {ObjectPath}", objectPath);
            return memoryStream;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to download video from output bucket: {ObjectPath}", objectPath);
            throw;
        }
    }

    /// <summary>
    /// Deletes a file from storage
    /// </summary>
    /// <param name="objectPath">The full GCS path (gs://bucket/object)</param>
    public async Task DeleteFileAsync(string objectPath)
    {
        try
        {
            var (bucketName, objectName) = ParseGcsPath(objectPath);
            
            _logger.LogInformation("Deleting file from storage: {BucketName}/{ObjectName}", 
                bucketName, objectName);

            await _storageClient.DeleteObjectAsync(bucketName, objectName);
            
            _logger.LogInformation("Successfully deleted file: {ObjectPath}", objectPath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to delete file from storage: {ObjectPath}", objectPath);
            throw;
        }
    }

    /// <summary>
    /// Gets the size of a file in storage
    /// </summary>
    /// <param name="objectPath">The full GCS path (gs://bucket/object)</param>
    /// <returns>The file size in bytes</returns>
    public async Task<long> GetFileSizeAsync(string objectPath)
    {
        try
        {
            var (bucketName, objectName) = ParseGcsPath(objectPath);
            
            var obj = await _storageClient.GetObjectAsync(bucketName, objectName);
            return (long)(obj.Size ?? 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get file size: {ObjectPath}", objectPath);
            throw;
        }
    }

    /// <summary>
    /// Checks if a file exists in storage
    /// </summary>
    /// <param name="objectPath">The full GCS path (gs://bucket/object)</param>
    /// <returns>True if the file exists, false otherwise</returns>
    public async Task<bool> FileExistsAsync(string objectPath)
    {
        try
        {
            var (bucketName, objectName) = ParseGcsPath(objectPath);
            
            var obj = await _storageClient.GetObjectAsync(bucketName, objectName);
            return obj != null;
        }
        catch (Google.GoogleApiException ex) when (ex.HttpStatusCode == System.Net.HttpStatusCode.NotFound)
        {
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check if file exists: {ObjectPath}", objectPath);
            throw;
        }
    }

    /// <summary>
    /// Generates the output path for a compressed video
    /// </summary>
    /// <param name="inputPath">The input file path</param>
    /// <param name="quality">The compression quality</param>
    /// <returns>The output path in the output bucket</returns>
    public string GenerateOutputPath(string inputPath, string quality)
    {
        var inputFileName = Path.GetFileNameWithoutExtension(ParseGcsPath(inputPath).objectName);
        var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
        var outputObjectName = $"output/{DateTime.UtcNow:yyyy/MM/dd}/{inputFileName}_{quality}_{timestamp}.mp4";
        
        return $"gs://{_config.OutputBucketName}/{outputObjectName}";
    }

    /// <summary>
    /// Parses a GCS path into bucket name and object name
    /// </summary>
    /// <param name="gcsPath">The full GCS path (gs://bucket/object)</param>
    /// <returns>A tuple containing the bucket name and object name</returns>
    private static (string bucketName, string objectName) ParseGcsPath(string gcsPath)
    {
        if (!gcsPath.StartsWith("gs://"))
        {
            throw new ArgumentException("Invalid GCS path format. Expected gs://bucket/object", nameof(gcsPath));
        }

        var pathWithoutPrefix = gcsPath.Substring(5); // Remove "gs://"
        var firstSlashIndex = pathWithoutPrefix.IndexOf('/');
        
        if (firstSlashIndex == -1)
        {
            throw new ArgumentException("Invalid GCS path format. Expected gs://bucket/object", nameof(gcsPath));
        }

        var bucketName = pathWithoutPrefix.Substring(0, firstSlashIndex);
        var objectName = pathWithoutPrefix.Substring(firstSlashIndex + 1);

        return (bucketName, objectName);
    }
}
