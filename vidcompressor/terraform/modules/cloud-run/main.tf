# Cloud Run Module - Serverless Backend Deployment
# Use this module when you want to deploy to Cloud Run instead of running locally

variable "project_id" {
  description = "Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "Google Cloud region"
  type        = string
}

variable "environment" {
  description = "Environment (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "service_name" {
  description = "Name of the Cloud Run service"
  type        = string
  default     = "vidcompressor-backend"
}

variable "service_account_email" {
  description = "Service account email for the Cloud Run service"
  type        = string
}

variable "docker_image" {
  description = "Docker image for the backend"
  type        = string
}

variable "input_bucket_name" {
  description = "Input bucket name"
  type        = string
}

variable "output_bucket_name" {
  description = "Output bucket name"
  type        = string
}

variable "temp_bucket_name" {
  description = "Temporary bucket name"
  type        = string
  default     = ""
}

variable "compression_queue_name" {
  description = "Cloud Tasks queue name"
  type        = string
}

variable "database_connection_string" {
  description = "Database connection string"
  type        = string
  sensitive   = true
  default     = ""
}

variable "cpu_limit" {
  description = "CPU limit for the Cloud Run service"
  type        = string
  default     = "1"
}

variable "memory_limit" {
  description = "Memory limit for the Cloud Run service"
  type        = string
  default     = "1Gi"
}

variable "min_instances" {
  description = "Minimum number of instances"
  type        = number
  default     = 0
}

variable "max_instances" {
  description = "Maximum number of instances"
  type        = number
  default     = 10
}

variable "timeout_seconds" {
  description = "Request timeout in seconds"
  type        = number
  default     = 3600  # 1 hour for video processing
}

variable "allow_public_access" {
  description = "Whether to allow public access to the service"
  type        = bool
  default     = true
}

variable "custom_environment_variables" {
  description = "Additional environment variables"
  type        = map(string)
  default     = {}
}

# Cloud Run service for the backend
resource "google_cloud_run_v2_service" "backend" {
  name     = "${var.service_name}-${var.environment}"
  location = var.region

  template {
    service_account = var.service_account_email
    timeout         = "${var.timeout_seconds}s"

    containers {
      image = var.docker_image

      ports {
        container_port = 8080
      }

      # Core environment variables
      env {
        name  = "ASPNETCORE_ENVIRONMENT"
        value = title(var.environment)
      }

      env {
        name  = "GoogleCloud__ProjectId"
        value = var.project_id
      }

      env {
        name  = "GoogleCloud__Region"
        value = var.region
      }

      env {
        name  = "GoogleCloud__InputBucketName"
        value = var.input_bucket_name
      }

      env {
        name  = "GoogleCloud__OutputBucketName"
        value = var.output_bucket_name
      }

      dynamic "env" {
        for_each = var.temp_bucket_name != "" ? [1] : []
        content {
          name  = "GoogleCloud__TempBucketName"
          value = var.temp_bucket_name
        }
      }

      env {
        name  = "GoogleCloud__Transcoder__Location"
        value = var.region
      }

      env {
        name  = "CloudTasks__ProjectId"
        value = var.project_id
      }

      env {
        name  = "CloudTasks__QueueName"
        value = var.compression_queue_name
      }

      env {
        name  = "CloudTasks__Location"
        value = var.region
      }

      env {
        name  = "CloudTasks__HandlerUrl"
        value = "https://${var.service_name}-${var.environment}-${random_id.service_suffix.hex}-${var.region}.a.run.app"
      }

      # Database connection (if provided)
      dynamic "env" {
        for_each = var.database_connection_string != "" ? [1] : []
        content {
          name  = "ConnectionStrings__DefaultConnection"
          value = var.database_connection_string
        }
      }

      # Custom environment variables
      dynamic "env" {
        for_each = var.custom_environment_variables
        content {
          name  = env.key
          value = env.value
        }
      }

      resources {
        limits = {
          cpu    = var.cpu_limit
          memory = var.memory_limit
        }
      }
    }

    scaling {
      min_instance_count = var.min_instances
      max_instance_count = var.max_instances
    }

    labels = {
      environment = var.environment
      application = "vidcompressor"
      component   = "backend"
    }
  }
}

# Random suffix for unique service URLs
resource "random_id" "service_suffix" {
  byte_length = 4
}

# Make Cloud Run service publicly accessible (optional)
resource "google_cloud_run_service_iam_member" "public_access" {
  count    = var.allow_public_access ? 1 : 0
  service  = google_cloud_run_v2_service.backend.name
  location = google_cloud_run_v2_service.backend.location
  role     = "roles/run.invoker"
  member   = "allUsers"
}

# Outputs
output "backend_url" {
  value       = google_cloud_run_v2_service.backend.uri
  description = "Cloud Run service URL"
}

output "service_name" {
  value       = google_cloud_run_v2_service.backend.name
  description = "Cloud Run service name"
}

output "service_id" {
  value       = google_cloud_run_v2_service.backend.id
  description = "Cloud Run service ID"
}

output "service_location" {
  value       = google_cloud_run_v2_service.backend.location
  description = "Cloud Run service location"
}
