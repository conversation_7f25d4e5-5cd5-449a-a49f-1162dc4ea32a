# Storage Module - Cloud Storage buckets for video processing
# This module creates the storage infrastructure for video input/output

variable "project_id" {
  description = "Google Cloud project ID"
  type        = string
}

variable "region" {
  description = "Google Cloud region"
  type        = string
}

variable "environment" {
  description = "Environment (dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "lifecycle_age_days" {
  description = "Number of days after which files are automatically deleted"
  type        = number
  default     = 7
}

variable "enable_cors" {
  description = "Whether to enable CORS for direct uploads"
  type        = bool
  default     = true
}

variable "cors_origins" {
  description = "List of allowed CORS origins"
  type        = list(string)
  default     = ["http://localhost:3000", "http://localhost:5119"]
}

variable "storage_class" {
  description = "Storage class for the buckets"
  type        = string
  default     = "STANDARD"
  validation {
    condition = contains([
      "STANDARD", 
      "NEARLINE", 
      "COLDLINE", 
      "ARCHIVE"
    ], var.storage_class)
    error_message = "Storage class must be one of: STANDARD, NEARLINE, COLDLINE, ARCHIVE."
  }
}

# Input videos bucket
resource "google_storage_bucket" "input_videos" {
  name     = "${var.project_id}-vidcompressor-input-${var.environment}"
  location = var.region

  # Cost optimization: lifecycle management
  lifecycle_rule {
    condition {
      age = var.lifecycle_age_days
    }
    action {
      type = "Delete"
    }
  }
  
  # Optional CORS configuration
  dynamic "cors" {
    for_each = var.enable_cors ? [1] : []
    content {
      origin          = var.cors_origins
      method          = ["GET", "HEAD", "PUT", "POST", "DELETE"]
      response_header = ["*"]
      max_age_seconds = 3600
    }
  }
  
  storage_class = var.storage_class
  
  # Uniform bucket-level access
  uniform_bucket_level_access = true
  
  # Labels for organization
  labels = {
    environment = var.environment
    purpose     = "video-input"
    application = "vidcompressor"
  }
}

# Output videos bucket
resource "google_storage_bucket" "output_videos" {
  name     = "${var.project_id}-vidcompressor-output-${var.environment}"
  location = var.region

  # Cost optimization: lifecycle management
  lifecycle_rule {
    condition {
      age = var.lifecycle_age_days
    }
    action {
      type = "Delete"
    }
  }
  
  storage_class = var.storage_class
  
  # Uniform bucket-level access
  uniform_bucket_level_access = true
  
  # Labels for organization
  labels = {
    environment = var.environment
    purpose     = "video-output"
    application = "vidcompressor"
  }
}

# Optional: Bucket for temporary files (shorter lifecycle)
resource "google_storage_bucket" "temp_files" {
  name     = "${var.project_id}-vidcompressor-temp-${var.environment}"
  location = var.region

  # Very aggressive cleanup for temp files
  lifecycle_rule {
    condition {
      age = 1  # Delete after 1 day
    }
    action {
      type = "Delete"
    }
  }
  
  storage_class = "STANDARD"  # Always use standard for temp files
  
  # Uniform bucket-level access
  uniform_bucket_level_access = true
  
  # Labels for organization
  labels = {
    environment = var.environment
    purpose     = "temporary-files"
    application = "vidcompressor"
  }
}

# Outputs
output "input_bucket_name" {
  value       = google_storage_bucket.input_videos.name
  description = "Name of the input videos bucket"
}

output "input_bucket_url" {
  value       = google_storage_bucket.input_videos.url
  description = "URL of the input videos bucket"
}

output "output_bucket_name" {
  value       = google_storage_bucket.output_videos.name
  description = "Name of the output videos bucket"
}

output "output_bucket_url" {
  value       = google_storage_bucket.output_videos.url
  description = "URL of the output videos bucket"
}

output "temp_bucket_name" {
  value       = google_storage_bucket.temp_files.name
  description = "Name of the temporary files bucket"
}

output "temp_bucket_url" {
  value       = google_storage_bucket.temp_files.url
  description = "URL of the temporary files bucket"
}

output "all_bucket_names" {
  value = {
    input  = google_storage_bucket.input_videos.name
    output = google_storage_bucket.output_videos.name
    temp   = google_storage_bucket.temp_files.name
  }
  description = "Map of all bucket names"
}
