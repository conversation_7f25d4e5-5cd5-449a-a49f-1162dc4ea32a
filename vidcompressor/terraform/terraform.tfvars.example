# Example Terraform Variables for VidCompressor
# Copy this file to terraform.tfvars and customize for your deployment

# =============================================================================
# REQUIRED VARIABLES
# =============================================================================

# Your Google Cloud project ID
project_id = "your-gcp-project-id"

# Google Cloud region
region = "us-central1"

# Environment (dev, staging, prod)
environment = "dev"

# =============================================================================
# DEPLOYMENT SCENARIOS
# =============================================================================

# Scenario 1: Local Development (Default)
# - Local frontend, backend, database
# - Cloud storage, transcoder, tasks
# - Cost: ~$1-5/month
enable_database  = false
enable_cloud_run = false
enable_secrets   = false
create_local_key_file = true

# Scenario 2: Hybrid Deployment
# - Local frontend and backend
# - Cloud database, storage, transcoder, tasks
# - Cost: ~$11-20/month
# enable_database  = true
# enable_cloud_run = false
# enable_secrets   = false
# create_local_key_file = true

# Scenario 3: Full Cloud Deployment
# - Cloud everything
# - Cost: ~$11-45/month
# enable_database  = true
# enable_cloud_run = true
# enable_secrets   = true
# create_local_key_file = false

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

# How long to keep files in storage (days)
storage_lifecycle_days = 7

# Enable CORS for direct uploads
enable_storage_cors = true

# Allowed origins for CORS
storage_cors_origins = [
  "http://localhost:3000",
  "http://localhost:5119",
  "https://your-domain.com"
]

# Storage class (STANDARD, NEARLINE, COLDLINE, ARCHIVE)
storage_class = "STANDARD"

# =============================================================================
# TASK QUEUE CONFIGURATION
# =============================================================================

# Task processing limits
task_max_concurrent_dispatches = 5
task_max_dispatches_per_second = 2
task_max_attempts = 3

# =============================================================================
# DATABASE CONFIGURATION (if enable_database = true)
# =============================================================================

# Database password (change this!)
database_password = "your-secure-database-password"

# =============================================================================
# CLOUD RUN CONFIGURATION (if enable_cloud_run = true)
# =============================================================================

# Service name
cloud_run_service_name = "vidcompressor-backend"

# Docker image (build and push your image first)
docker_image = "us-central1-docker.pkg.dev/your-project-id/vidcompressor-registry/backend:latest"

# Resource limits
cloud_run_cpu_limit = "1"
cloud_run_memory_limit = "1Gi"

# Scaling
cloud_run_min_instances = 0  # Scale to zero
cloud_run_max_instances = 10

# Access control
cloud_run_allow_public_access = true

# Custom environment variables
cloud_run_custom_env_vars = {
  "CUSTOM_VAR" = "custom_value"
}

# =============================================================================
# SECRETS CONFIGURATION (if enable_secrets = true)
# =============================================================================

# Whether to populate secrets with values
create_secret_versions = false

# OAuth credentials (only if create_secret_versions = true)
# google_client_id = "your-google-client-id"
# google_client_secret = "your-google-client-secret"
# jwt_secret = "your-jwt-secret"
