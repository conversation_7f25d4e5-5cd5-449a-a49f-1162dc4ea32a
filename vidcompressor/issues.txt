when i upload to a user's google photos library i'd like to add the uploaded media to a 'Gallery Tuner' album so they can easily find all of their compressed media. We should keep track of whether we have created the album or not for that user so we don't try to recreate it. Keep in mind the changes to the google photos APIs that took place recently, we may need more scopes: 
Updates to the Google Photos APIs

bookmark_border

Posted September 2024.

This page provides details on specific changes being made to the Google Photos APIs. Review the details on this page to understand how the methods and scopes you use may be affected. You can read more about the announcement of these changes in the related blog post.

Note: All changes take effect March 2025.
Library API: Impact on common use cases
The following sections detail detail how the upcoming changes will impact common use cases.

Uploading media items and creating albums
What's changing: No change. You can continue to upload media items and create albums using the photoslibrary.appendonly scope.

What you can do:

If your app is uploading media items or creating albums, make sure your using the photoslibrary.appendonly scope, and not the photoslibrary scope. The photoslibrary scope is being removed.

Listing, searching, and retrieving media items and albums
What's changing: You can now only list, search, and retrieve albums and media items that were created by your app.

What you can do:

If your app needs users to select photos or albums from their entire library, use to the new Google Photos Picker API. This provides a secure and user-friendly way for users to grant access to specific content.
If your app relies on accessing the user's entire library, you may need to re-evaluate your app or consider alternative approaches.
Sharing and shared albums
What's changing: Shared albums and the associated API functions (share, unshare, get, join, leave, and list) will return a 403 PERMISSION_DENIED after March 31, 2025.

What you can do:

Direct users to the Google Photos app to manage sharing themselves. You can provide clear instructions or deep links within your app to guide them.

Managing app-created albums: enrichments and album contents
What's changing: The photoslibrary.edit.appcreateddata is being added to the following three methods for conceptual consistency:

albums.addEnrichment
albums.batchAddMediaItems
albums.batchRemoveMediaItems
What you can do:

If your app already uses these methods, consider adopting the photoslibrary.edit.appcreateddata scope to simplify your authorization process.
Library API: Affected scopes and methods
As part of our changes to the Google Photos APIs, we have made the following updates.

As shown on the updated Authorization page, the following scopes will be removed from the Library API after March 31, 2025:

photoslibrary.readonly
photoslibrary.sharing
photoslibrary
The following scopes will remain:

photoslibrary.appendonly
photoslibrary.readonly.appcreateddata
photoslibrary.edit.appcreateddata
This tables details the specific Library API methods and scopes affected.

Updates	Methods	Scopes
These methods can now only be used with albums and media items created by your app.	
albums.create
mediaItems.batchCreate
Scopes remaining:
photoslibrary.appendonly
Scopes removed:
photoslibrary
photoslibrary.sharing
albums.get
albums.list
mediaItems.batchGet
mediaItems.get
mediaItems.list
mediaItems.search
Scopes remaining:
photoslibrary.readonly.appcreateddata
Scopes removed:
photoslibrary
photoslibrary.readonly
These methods will no longer be available.	
albums.share
albums.unshare
sharedAlbums.get
sharedAlbums.join
sharedAlbums.leave
sharedAlbums.list
Scopes remaining:
None
Scopes removed:
photoslibrary.sharing
Note: sharedAlbums.list is being removed because there will no longer be shared albums created by your app available to list.
These methods will have the photoslibrary.edit.appcreateddata added.
These methods can now only be used with albums and media items created by your app.	
albums.addEnrichment
albums.batchAddMediaItems
Scopes remaining:
photoslibrary.appendonly
photoslibrary.edit.appcreateddata
Scopes removed:
photoslibrary
albums.batchRemoveMediaItems
Scopes remaining:
photoslibrary.edit.appcreateddata
Scopes removed:
photoslibrary
These methods will remain unchanged.	
albums.patch
mediaItems.patch
Scopes remaining:
photoslibrary.edit.appcreateddata
Scopes removed:
None