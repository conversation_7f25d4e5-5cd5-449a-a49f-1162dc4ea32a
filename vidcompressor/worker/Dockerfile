FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /app

# Copy project files for restore
COPY worker/*.csproj ./worker/
COPY shared/*.csproj ./shared/

# Restore dependencies
WORKDIR /app/worker
RUN dotnet restore

# Copy source code
WORKDIR /app
COPY worker/ ./worker/
COPY shared/ ./shared/

# Build and publish
WORKDIR /app/worker
RUN dotnet publish -c Release -o out

# Build runtime image
FROM mcr.microsoft.com/dotnet/aspnet:8.0
WORKDIR /app
COPY --from=build /app/worker/out .

# Install FFmpeg
RUN apt-get update && apt-get install -y ffmpeg

ENTRYPOINT ["dotnet", "worker.dll"]
