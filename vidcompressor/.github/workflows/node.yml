name: Node.js CI

on:
  push:
    branches: [ "main" ]
    paths:
      - 'frontend/**'
  pull_request:
    branches: [ "main" ]
    paths:
      - 'frontend/**'

jobs:
  build:

    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: ./frontend

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
    - uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    - run: npm ci
    - run: npm run build --if-present
    - run: npm test
