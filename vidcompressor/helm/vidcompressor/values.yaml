frontend:
  replicaCount: 1
  image:
    repository: gcr.io/your-project-id/frontend
    tag: latest
  service:
    type: LoadBalancer
    port: 80
    targetPort: 3000

backend:
  replicaCount: 1
  image:
    repository: gcr.io/your-project-id/backend
    tag: latest
  service:
    type: ClusterIP
    port: 80
    targetPort: 5000
  env:
    googleCloud:
      projectId: "your-project-id"
      region: "us-central1"
      inputBucket: "your-project-id-vidcompressor-input"
      outputBucket: "your-project-id-vidcompressor-output"
      serviceAccountKeyPath: "/secrets/transcoder-service-account-key"
      transcoderLocation: "us-central1"
