#!/bin/bash

echo "🚀 Starting VidCompressor Local Development Environment"

# Start infrastructure services
echo "📦 Starting PostgreSQL and Redis..."
docker-compose up postgres redis -d

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check if services are healthy
echo "🔍 Checking service health..."
docker-compose ps

echo ""
echo "✅ Infrastructure is ready!"
echo ""
echo "Now you can run your .NET services locally:"
echo ""
echo "🔧 Backend (in a new terminal):"
echo "   cd backend && dotnet run"
echo ""
echo "⚙️  Worker (in another terminal):"
echo "   cd worker && dotnet run"
echo ""
echo "🌐 Your services will be available at:"
echo "   - Backend API: http://localhost:5119 (or check launchSettings.json)"
echo "   - PostgreSQL: localhost:5432"
echo "   - Redis: localhost:6379"
echo ""
echo "🛑 To stop infrastructure:"
echo "   docker-compose down"
